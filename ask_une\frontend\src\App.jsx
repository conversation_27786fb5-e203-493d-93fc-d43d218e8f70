import { useState } from "react";
import axios from "axios";

function App() {
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState("");
  const [sessionId, setSessionId] = useState(null);

  const sendMessage = async () => {
    if (!input.trim()) return;

    const userMsg = { sender: "You", text: input };
    setMessages([...messages, userMsg]);

    try {
      const res = await axios.post("/api/chat/", {
        text: input,
        session_id: sessionId,
      });

      const data = res.data;
      setSessionId(data.session_id);

      const agentMsg = { sender: data.agent, text: data.text };
      setMessages((prev) => [...prev, agentMsg]);
    } catch (err) {
      console.error(err);
    }

    setInput("");
  };

  return (
    <div style={{ maxWidth: "600px", margin: "20px auto", fontFamily: "Arial" }}>
      <h1>🎓 University Support Assistant</h1>
      <div style={{ border: "1px solid #ccc", padding: "10px", height: "400px", overflowY: "scroll" }}>
        {messages.map((m, i) => (
          <div key={i} style={{ margin: "5px 0" }}>
            <b>{m.sender}:</b> {m.text}
          </div>
        ))}
      </div>
      <div style={{ marginTop: "10px" }}>
        <input
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Ask me anything..."
          style={{ width: "75%", padding: "8px" }}
        />
        <button onClick={sendMessage} style={{ padding: "8px 12px", marginLeft: "5px" }}>
          Send
        </button>
      </div>
    </div>
  );
}

export default App;
