import React, { useState, useEffect } from "react";

const API_BASE = "http://localhost:8000/api";

export default function App() {
  const [sessionId, setSessionId] = useState(localStorage.getItem("session_id"));
  const [input, setInput] = useState("");
  const [messages, setMessages] = useState([]); // {sender, text}
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!sessionId) {
      createSession();
    } else {
      // optionally load history
      fetch(`${API_BASE}/history/${sessionId}/`).then(async (res) => {
        if (res.ok) {
          const data = await res.json();
          setMessages(data.messages.map(m => ({ sender: m.sender, text: m.text })));
        }
      });
    }
  }, []);

  async function createSession() {
    const res = await fetch(`${API_BASE}/session/`, { method: "POST" });
    const data = await res.json();
    setSessionId(data.session_id);
    localStorage.setItem("session_id", data.session_id);
  }

  async function sendMessage() {
    if (!input.trim()) return;
    const userMsg = { sender: "You", text: input };
    setMessages(prev => [...prev, userMsg]);
    setLoading(true);

    const res = await fetch(`${API_BASE}/message/`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ session_id: sessionId, text: input }),
    });
    const data = await res.json();
    setLoading(false);

    if (res.ok) {
      setMessages(prev => [...prev, { sender: data.agent, text: data.text }]);
      setInput("");
    } else {
      setMessages(prev => [...prev, { sender: "System", text: data.error || "Unknown error" }]);
    }
  }

  async function clearChat() {
    const res = await fetch(`${API_BASE}/clear/`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ session_id: sessionId }),
    });
    const data = await res.json();
    setSessionId(data.session_id);
    localStorage.setItem("session_id", data.session_id);
    setMessages([]);
  }

  return (
    <div style={{ maxWidth: 760, margin: "20px auto", fontFamily: "Arial, sans-serif" }}>
      <h2>University Multi-Agent Support</h2>
      <div style={{ border: "1px solid #ddd", padding: 12, minHeight: 300 }}>
        {messages.length === 0 && <div style={{ color: "#666" }}>Start a conversation — ask about courses, schedules, or ask for a haiku.</div>}
        {messages.map((m, idx) => (
          <div key={idx} style={{ margin: "10px 0" }}>
            <div style={{ fontWeight: "600", fontSize: 14 }}>{m.sender}</div>
            <div style={{ paddingLeft: 8 }}>{m.text}</div>
          </div>
        ))}
        {loading && <div style={{ color: "#888", marginTop: 10 }}>Thinking…</div>}
      </div>

      <div style={{ display: "flex", marginTop: 12 }}>
        <input value={input} onChange={(e) => setInput(e.target.value)} style={{ flex: 1, padding: 8 }} placeholder="Type your question..." />
        <button onClick={sendMessage} style={{ marginLeft: 8, padding: "8px 12px" }}>Send</button>
        <button onClick={clearChat} style={{ marginLeft: 8, padding: "8px 12px" }}>Clear</button>
      </div>

      <div style={{ marginTop: 12, color: "#666", fontSize: 13 }}>
        <div>Session id: <code>{sessionId}</code></div>
        <div style={{ marginTop: 6 }}>Tip: Try “What courses should I take next semester if I’m interested in data science?” or “Write me a poem about the university cafeteria.”</div>
      </div>
    </div>
  );
}
cl