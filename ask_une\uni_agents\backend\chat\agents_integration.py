import os
import json
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv
from django.conf import settings

load_dotenv()
try:
    # Preferred: official Agents SDK
    from agents import Agent, Runner, tool, Handoff, function_tool
    from openai import OpenAI
except Exception:
    # fallback imports to make errors clear if the package differs
    raise ImportError("Could not import Agents SDK modules. Please ensure you installed the OpenAI Agents SDK per official docs.")

from .tools import course_lookup, academic_calendar

# OpenAI client (Responses API) - not used directly here, but SDK will use it under the hood
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY") 
if not OPENAI_API_KEY:
    raise RuntimeError("OPENAI_API_KEY environment variable must be set.")

# Example function-tool wrappers so agents can call our local functions
@function_tool
def tool_course_lookup(topic: str = "data science", level: str = "undergrad", limit: int = 4) -> Dict:
    return course_lookup(topic=topic, level=level, limit=limit)

@function_tool
def tool_academic_calendar(query: str = "") -> Dict:
    return academic_calendar(query=query)

# Build specialist agents
course_advisor_agent = Agent(
    name="Course Advisor",
    instructions=(
        "You are Course Advisor. You answer course selection and academic planning questions in a helpful "
        "and factual tone. When helpful, call the 'tool_course_lookup' tool to fetch recommended courses. "
        "Ask follow-up questions only if necessary to recommend better courses (e.g., year, major, preferences)."
    ),
    tools=[tool_course_lookup],
)

university_poet_agent = Agent(
    name="University Poet",
    instructions=(
        "You are University Poet. You respond ONLY in haiku (5-7-5 syllables) about campus culture and social life. "
        "If the user asks for schedules or course advice, politely refuse and suggest the appropriate assistant by name."
    ),
    tools=[],
)

scheduling_agent = Agent(
    name="Scheduling Assistant",
    instructions=(
        "You are Scheduling Assistant. Provide class times, exam schedules, and key academic dates in concise, factual sentences. "
        "Use the 'tool_academic_calendar' to fetch calendar facts if needed."
    ),
    tools=[tool_academic_calendar],
)

# Triage agent with handoffs to available specialists
triage_agent = Agent(
    name="Triage Agent",
    instructions=(
        "You are the Triage Agent. Decide which specialist should handle the user's request: Course Advisor (courses/planning), "
        "University Poet (poems/haiku about campus/social life), or Scheduling Assistant (dates/schedules). "
        "If uncertain, ask a one-sentence clarifying question. If you decide a specific specialist should handle the request, use a handoff."
    ),
    # register handoff options (agents-as-tools)
    handoffs=[course_advisor_agent, university_poet_agent, scheduling_agent],
)

# Runner to execute agent runs on demand
#runner = Runner.from_agents([triage_agent, course_advisor_agent, university_poet_agent, scheduling_agent], openai_api_key=OPENAI_API_KEY)

def run_triage_and_handle(session_messages: List[Dict[str, Any]], user_text: str) -> Dict[str, Any]:
    """
    Run triage agent with session context; execute handoffs / tools as requested and return final response.
    session_messages: list of {'sender':..., 'text':...} (most recent first or in order).
    Returns: { 'agent': agent_name, 'text': ..., 'tool_calls': [...], 'events': [...] }
    """
    # Build context for triage
    # The Runner / Agent SDK usually accepts a list of message dicts as context; adjust per SDK API
    triage_input = {
        "messages": [{"role": "user", "content": user_text}],
        "session_history": session_messages[-10:],  # include last 10 items
    }

    # Run triage; Runner API will produce events including handoff decisions
    run =  Runner.run(agent=triage_agent, input=user_text, context=session_messages)
    # The run will contain events; find handoff or final reply
    events = run.events if hasattr(run, "events") else getattr(run, "responses", [])
    # Simple event navigation: if triage selected a handoff, the SDK will produce a handoff event.
    # To keep compatibility across SDK versions, we inspect run.output or events.

    # If triage handed off directly to a specialist, the SDK may have executed it; otherwise, we must trigger it.
    # For robustness, check if run produced an 'handoff' to an agent:
    handoff_target = None
    if hasattr(run, "handoff") and run.handoff:
        handoff_target = run.handoff.agent.name
    else:
        # Try to parse events
        for ev in getattr(run, "events", []) or []:
            if ev.get("type") == "handoff":
                handoff_target = ev.get("agent", {}).get("name")
                break

    if not handoff_target:
        # If triage replied itself, return that reply
        triage_reply = run.output if hasattr(run, "output") else (getattr(run, "final_text", "") or "")
        return {"agent": "Triage Agent", "text": triage_reply, "events": events}

    # else: call the target agent with same context
    target_agent = {
        "Course Advisor": course_advisor_agent,
        "University Poet": university_poet_agent,
        "Scheduling Assistant": scheduling_agent,
    }.get(handoff_target)

    if not target_agent:
        return {"agent": "Triage Agent", "text": f"Sorry, I can't hand off to {handoff_target}.", "events": events}

    # Run the target agent. The Runner will handle function tool calls (our @function_tool decorated tools)
    sub_run = runner.run(agent=target_agent, input=user_text, context=session_messages)
    # The sub_run should include tool outputs if called; extract final text
    final_text = getattr(sub_run, "output", None) or getattr(sub_run, "final_text", None) or ""
    # If tools returned results, they can be in sub_run.tool_results or sub_run.events; include them
    tool_calls = []
    for ev in getattr(sub_run, "events", []) or []:
        if ev.get("type") == "tool_call" or ev.get("type") == "function_call":
            tool_calls.append(ev)

    return {"agent": target_agent.name, "text": final_text, "tool_calls": tool_calls, "events": events}
