# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing import Union, Iterable, Optional
from typing_extensions import Literal, Required, TypeAlias, TypedDict

__all__ = ["ResponseCodeInterpreterToolCallParam", "Output", "OutputLogs", "OutputImage"]


class OutputLogs(TypedDict, total=False):
    logs: Required[str]
    """The logs output from the code interpreter."""

    type: Required[Literal["logs"]]
    """The type of the output. Always 'logs'."""


class OutputImage(TypedDict, total=False):
    type: Required[Literal["image"]]
    """The type of the output. Always 'image'."""

    url: Required[str]
    """The URL of the image output from the code interpreter."""


Output: TypeAlias = Union[OutputLogs, OutputImage]


class ResponseCodeInterpreterToolCallParam(TypedDict, total=False):
    id: Required[str]
    """The unique ID of the code interpreter tool call."""

    code: Required[Optional[str]]
    """The code to run, or null if not available."""

    container_id: Required[str]
    """The ID of the container used to run the code."""

    outputs: Required[Optional[Iterable[Output]]]
    """The outputs generated by the code interpreter, such as logs or images.

    Can be null if no outputs are available.
    """

    status: Required[Literal["in_progress", "completed", "incomplete", "interpreting", "failed"]]
    """The status of the code interpreter tool call.

    Valid values are `in_progress`, `completed`, `incomplete`, `interpreting`, and
    `failed`.
    """

    type: Required[Literal["code_interpreter_call"]]
    """The type of the code interpreter tool call. Always `code_interpreter_call`."""
