# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from typing import List, Union, Optional
from typing_extensions import Literal, Annotated, TypeAlias

from ..._utils import PropertyInfo
from ..._models import BaseModel

__all__ = ["ResponseCodeInterpreterToolCall", "Output", "OutputLogs", "OutputImage"]


class OutputLogs(BaseModel):
    logs: str
    """The logs output from the code interpreter."""

    type: Literal["logs"]
    """The type of the output. Always 'logs'."""


class OutputImage(BaseModel):
    type: Literal["image"]
    """The type of the output. Always 'image'."""

    url: str
    """The URL of the image output from the code interpreter."""


Output: TypeAlias = Annotated[Union[OutputLogs, OutputImage], PropertyInfo(discriminator="type")]


class ResponseCodeInterpreterToolCall(BaseModel):
    id: str
    """The unique ID of the code interpreter tool call."""

    code: Optional[str] = None
    """The code to run, or null if not available."""

    container_id: str
    """The ID of the container used to run the code."""

    outputs: Optional[List[Output]] = None
    """The outputs generated by the code interpreter, such as logs or images.

    Can be null if no outputs are available.
    """

    status: Literal["in_progress", "completed", "incomplete", "interpreting", "failed"]
    """The status of the code interpreter tool call.

    Valid values are `in_progress`, `completed`, `incomplete`, `interpreting`, and
    `failed`.
    """

    type: Literal["code_interpreter_call"]
    """The type of the code interpreter tool call. Always `code_interpreter_call`."""
